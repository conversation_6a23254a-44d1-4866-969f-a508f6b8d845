import base64
import requests
from dataclasses import dataclass
import os
import json
import pydantic
from PIL import Image, ImageOps
from io import BytesIO

API_KEY = "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"
if not API_KEY:
    raise ValueError("Please set the OPENROUTER_API_KEY environment variable")


@dataclass
class PreprocessingResult:
    image_base64: str
    language: str
    description: str
    messages: list



def run_preprocessing(image_path: str) -> PreprocessingResult:
    # Load image with <PERSON>llow, autorotate, and downscale
    with Image.open(image_path) as img:
        # Apply autorotation based on EXIF
        img = ImageOps.exif_transpose(img)

        # Compute resize ratio so shortest side = 1024
        w, h = img.size
        scale = 1024 / min(w, h)
        new_size = (int(w * scale), int(h * scale))
        img = img.resize(new_size, Image.LANCZOS)

        # Save to memory buffer as JPEG
        buffer = BytesIO()
        img.save(buffer, format="JPEG")
        image_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")

    # OpenRouter API endpoint
    url = "https://openrouter.ai/api/v1/chat/completions"

    messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "You are an expert translation app. You translate to English. You provide a short helpful description for a user exploring new cultures or just trying to learn a new language. Return a strucured JSON with the following fields: language (2 letter codes like en, it, es, fr, de, cs, etc.), description"
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}
                    }
                ],
            },
            {
                "role": "assistant",
                "content": [{"type": "text", "text": "```json\n{\"language\": \""}],
            },
        ]

    payload = {
        "model": "google/gemini-2.5-flash",
        "messages": messages,
    }

    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
    }

    response = requests.post(url, headers=headers, json=payload)

    reply = response.json()["choices"][0]["message"]["content"]
    messages.append({"role": "assistant", "content": reply})

    # Print nicely
    json_str = "```json\n{\"language\": \""+reply

    #strip the ```json\n from the beginning and the ``` from the end
    json_str = json_str[8:-3]

    print(json_str)
    decoded_json = json.loads(json_str)

    
    return PreprocessingResult(image_base64, decoded_json["language"], decoded_json["description"], messages)




#change to pydantic
class OCRBox(pydantic.BaseModel):
    lt: list[int, int]
    rb: list[int, int]
    text: str
    prob: float



class OCROutText:
    indexes: list[int]
    text: str


#this step continues the conversation with the llm but asks a different question
def run_postprocessing(messages: list, ocr_output: list[OCRBox]) -> str:

    #we nned to add indexes to the ocr_outputs, lets create a hashmap(in python its a dict)
    ocr_dict = {i: box.model_dump() for i, box in enumerate(ocr_output)}

    #serialize the ocr_dict to json
    ocr_json = json.dumps(ocr_dict)
    print(ocr_json)




    messages.extend(
        [
            {
                "role": "user",
                "content": [{"type": "text", "text": """
                             Here are all the bounding boxes and OCRed text from the image.
                             The OCR is not always correct. Please correct the OCR errors and return a structured JSON with the following format:
                             [{indexes: [int], text: str}]
                             indexes is a list of the indexes of the bounding boxes that make up the text. (sometimes multiple bounding boxes are part of the same phrase where translation might change the order of the words)
                             you dont have to include all the bounding boxes
                             text is the translated text.
                             THE OCR OUTPUT:
                             """ + ocr_json}],
            },
            {
                "role": "assistant",
                "content": [{"type": "text", "text": "```json\n[{\"indexes\": ["}],
            }

        ]
    )
    
    url = "https://openrouter.ai/api/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
    }
    payload = {
        "model": "google/gemini-2.5-flash",
        "messages": messages,
    }
    response = requests.post(url, headers=headers, json=payload)
    reply = response.json()["choices"][0]["message"]["content"]
    messages.append({"role": "assistant", "content": reply})

    json_str = "```json\n[{\"indexes\": ["+reply

    json_str = json_str[8:-3]
    print(json_str)
    decoded_json = json.loads(json_str)
    return decoded_json


    



if __name__ == "__main__":
    run_preprocessing("hard.jpg")