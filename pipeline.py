from dataclasses import dataclass
import llm, easy_ocr
import cv2


def draw_translation_results(image_path: str, ocr_boxes: list[llm.OCRBox], postprocessing_result: list):
    """
    Draw translation results on the image.

    Args:
        image_path: Path to the original image
        ocr_boxes: List of OCRBox objects from OCR
        postprocessing_result: List of translation results with indexes and text
    """
    # Load the image again for the translation visualization
    image = cv2.imread(image_path)

    # Keep track of which boxes have been processed
    processed_boxes = set()

    # Process each translation result
    for result in postprocessing_result:
        indexes = result.get('indexes', [])
        translated_text = result.get('text', '')

        if not indexes:
            continue

        # Get all boxes for this translation
        boxes_for_translation = []
        for idx in indexes:
            if idx < len(ocr_boxes):
                boxes_for_translation.append(ocr_boxes[idx])
                processed_boxes.add(idx)

        if not boxes_for_translation:
            continue

        # Calculate merged bounding box
        all_x_coords = []
        all_y_coords = []

        for box in boxes_for_translation:
            all_x_coords.extend([box.lt[0], box.rb[0]])
            all_y_coords.extend([box.lt[1], box.rb[1]])

        # Get the overall bounding box
        min_x, max_x = min(all_x_coords), max(all_x_coords)
        min_y, max_y = min(all_y_coords), max(all_y_coords)

        # Draw merged bounding box in blue
        cv2.rectangle(image, (min_x, min_y), (max_x, max_y), (255, 0, 0), 2)

        # Draw translated text in blue
        cv2.putText(image, translated_text, (min_x, min_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)

    # Draw any remaining unprocessed boxes with original text in gray
    for i, box in enumerate(ocr_boxes):
        if i not in processed_boxes:
            tx, ty = box.lt[0], box.lt[1]
            bx, by = box.rb[0], box.rb[1]

            # Draw bounding box in gray for untranslated text
            cv2.rectangle(image, (tx, ty), (bx, by), (128, 128, 128), 2)
            # Draw original text in gray
            cv2.putText(image, box.text, (tx, ty - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (128, 128, 128), 2)

    # Save the translation result image
    cv2.imwrite("translation_result.png", image)
    print("Translation visualization saved as 'translation_result.png'")


def main():
    image_path = "jar.jpg"

    preprocessing_result: llm.PreprocessingResult = llm.run_preprocessing(image_path)
    

    image, result = easy_ocr.run_easy_ocr(image_path, [preprocessing_result.language, "en"])
    #image, result = easy_ocr.run_easy_ocr(image_path, ["cs", "en"])

    #convert the result to a list of OCRBox objects


    ocr_boxes = [llm.OCRBox(lt=[int(box[0][0][0]), int(box[0][0][1])], rb=[int(box[0][2][0]), int(box[0][2][1])], text=box[1], prob=box[2]) for box in result]

    print(result)
    print(ocr_boxes)

    postprocessing_result = llm.run_postprocessing(preprocessing_result.messages, ocr_boxes)
    print(postprocessing_result)


    for (coord, text, prob) in result:
        (top_left, top_right, bottom_right, bottom_left) = coord
        tx, ty = (int(top_left[0]), int(top_left[1]))
        bx, by = (int(bottom_right[0]), int(bottom_right[1]))
        cv2.rectangle(image, (tx, ty), (bx, by), (0, 255, 0), 2)
        cv2.putText(image, text, (tx, ty - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)


    #save the image
    cv2.imwrite("easy_ocr_result.png", image)

    # Create a second image with translation results
    draw_translation_results(image_path, ocr_boxes, postprocessing_result)

if __name__ == "__main__":
    main()